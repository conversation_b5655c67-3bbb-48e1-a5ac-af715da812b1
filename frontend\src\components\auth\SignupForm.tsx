'use client'

import React, { useState } from 'react'
import { But<PERSON> } from '@/components/ui/Button'
import { Input } from '@/components/ui/Input'
import { AuthCard } from '@/components/ui/Card'
import { useAuth } from '@/contexts/AuthContext'
import { SignupData } from '@/types/auth'

interface SignupFormProps {
  onSuccess?: () => void
  onSwitchToLogin?: () => void
}

// Icons for enhanced UI
const UserIcon = () => (
  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={2}
      d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
    />
  </svg>
)

const EmailIcon = () => (
  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={2}
      d="M16 12a4 4 0 10-8 0 4 4 0 008 0zm0 0v1.5a2.5 2.5 0 005 0V12a9 9 0 10-9 9m4.5-1.206a8.959 8.959 0 01-4.5 1.207"
    />
  </svg>
)

const PasswordIcon = () => (
  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={2}
      d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"
    />
  </svg>
)

// const CheckIcon = () => (
//   <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
//     <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
//   </svg>
// )

const StarIcon = () => (
  <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
  </svg>
)

export const SignupForm: React.FC<SignupFormProps> = ({ onSuccess, onSwitchToLogin }) => {
  const { signup } = useAuth()
  const [formData, setFormData] = useState<SignupData>({
    email: '',
    password: '',
    name: ''
  })
  const [confirmPassword, setConfirmPassword] = useState('')
  const [errors, setErrors] = useState<Partial<SignupData & { confirmPassword: string; general: string }>>({})
  const [loading, setLoading] = useState(false)
  // const [currentStep, setCurrentStep] = useState(1)
  const [passwordStrength, setPasswordStrength] = useState(0)

  // Password strength calculation
  const calculatePasswordStrength = (password: string): number => {
    let strength = 0
    if (password.length >= 8) strength += 1
    if (/[a-z]/.test(password)) strength += 1
    if (/[A-Z]/.test(password)) strength += 1
    if (/[0-9]/.test(password)) strength += 1
    if (/[^A-Za-z0-9]/.test(password)) strength += 1
    return strength
  }

  const getPasswordStrengthText = (strength: number): string => {
    switch (strength) {
      case 0:
      case 1:
        return 'Very Weak'
      case 2:
        return 'Weak'
      case 3:
        return 'Fair'
      case 4:
        return 'Good'
      case 5:
        return 'Strong'
      default:
        return 'Very Weak'
    }
  }

  const getPasswordStrengthColor = (strength: number): string => {
    switch (strength) {
      case 0:
      case 1:
        return 'bg-red-500'
      case 2:
        return 'bg-orange-500'
      case 3:
        return 'bg-yellow-500'
      case 4:
        return 'bg-lime-500'
      case 5:
        return 'bg-green-500'
      default:
        return 'bg-gray-300'
    }
  }

  const validateForm = (): boolean => {
    const newErrors: typeof errors = {}

    if (!formData.name?.trim()) {
      newErrors.name = 'Full name is required'
    }

    if (!formData.email) {
      newErrors.email = 'Email is required'
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = 'Please enter a valid email address'
    }

    if (!formData.password) {
      newErrors.password = 'Password is required'
    } else if (formData.password.length < 8) {
      newErrors.password = 'Password must be at least 8 characters long'
    } else if (passwordStrength < 3) {
      newErrors.password = 'Please choose a stronger password'
    }

    if (!confirmPassword) {
      newErrors.confirmPassword = 'Please confirm your password'
    } else if (formData.password !== confirmPassword) {
      newErrors.confirmPassword = 'Passwords do not match'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!validateForm()) return

    setLoading(true)
    setErrors({})

    try {
      await signup(formData.email, formData.password, formData.name)
      onSuccess?.()
    } catch (error) {
      setErrors({ general: error instanceof Error ? error.message : 'Signup failed' })
    } finally {
      setLoading(false)
    }
  }

  const handleInputChange = (field: keyof SignupData) => (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value
    setFormData(prev => ({ ...prev, [field]: value }))

    // Calculate password strength when password changes
    if (field === 'password') {
      setPasswordStrength(calculatePasswordStrength(value))
    }

    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: undefined }))
    }
  }

  return (
    <AuthCard
      title="Create Your Account"
      subtitle="Join the premium real estate community"
      className="w-full max-w-md mx-auto"
    >
      {/* Progress Steps */}
      {/* <div className="mb-8">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-2">
            <div
              className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-semibold transition-all ${
                currentStep >= 1 ? 'bg-lime-600 text-white' : 'bg-slate-300 text-slate-600'
              }`}
            >
              {currentStep > 1 ? <CheckIcon /> : '1'}
            </div>
            <span className="text-sm font-medium text-slate-900">Personal Info</span>
          </div>
          <div className="flex-1 h-1 mx-4 bg-slate-300 rounded">
            <div
              className={`h-full bg-lime-600 rounded transition-all duration-300 ${
                currentStep >= 2 ? 'w-full' : 'w-0'
              }`}
            ></div>
          </div>
          <div className="flex items-center space-x-2">
            <div
              className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-semibold transition-all ${
                currentStep >= 2 ? 'bg-lime-600 text-white' : 'bg-slate-300 text-slate-600'
              }`}
            >
              {currentStep > 2 ? <CheckIcon /> : '2'}
            </div>
            <span className="text-sm font-medium text-slate-900">Security</span>
          </div>
        </div>
      </div> */}

      <form onSubmit={handleSubmit} className="space-y-6">
        {errors.general && (
          <div className="p-4 bg-destructive/10 border border-destructive/20 rounded-xl animate-slide-in-left">
            <div className="flex items-center gap-2">
              <svg className="w-5 h-5 text-destructive" fill="currentColor" viewBox="0 0 20 20">
                <path
                  fillRule="evenodd"
                  d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z"
                  clipRule="evenodd"
                />
              </svg>
              <p className="text-sm text-destructive font-medium">{errors.general}</p>
            </div>
          </div>
        )}

        <div className="space-y-5">
          <Input
            label="Full Name"
            type="text"
            value={formData.name}
            onChange={handleInputChange('name')}
            placeholder="Enter your full name"
            autoComplete="new-name"
            autoCorrect="off"
            spellCheck="false"
            error={errors.name}
            icon={<UserIcon />}
            variant="floating"
            required
          />

          <Input
            label="Email Address"
            type="email"
            value={formData.email}
            onChange={handleInputChange('email')}
            placeholder="Enter your email address"
            autoComplete="new-email"
            autoCorrect="off"
            spellCheck="false"
            error={errors.email}
            icon={<EmailIcon />}
            variant="floating"
            required
          />

          <div className="space-y-2">
            <Input
              label="Password"
              type="password"
              value={formData.password}
              onChange={handleInputChange('password')}
              placeholder="Create a strong password"
              autoComplete="new-password"
              error={errors.password}
              icon={<PasswordIcon />}
              variant="floating"
              required
            />

            {/* Password Strength Indicator */}
            {formData.password && (
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-xs text-slate-600">Password Strength:</span>
                  <span
                    className={`text-xs font-medium ${
                      passwordStrength >= 4
                        ? 'text-green-600'
                        : passwordStrength >= 3
                          ? 'text-lime-600'
                          : passwordStrength >= 2
                            ? 'text-yellow-600'
                            : 'text-red-600'
                    }`}
                  >
                    {getPasswordStrengthText(passwordStrength)}
                  </span>
                </div>
                <div className="flex space-x-1">
                  {[1, 2, 3, 4, 5].map(level => (
                    <div
                      key={level}
                      className={`h-2 flex-1 rounded-full transition-all duration-300 ${
                        level <= passwordStrength ? getPasswordStrengthColor(passwordStrength) : 'bg-slate-300'
                      }`}
                    />
                  ))}
                </div>
                <div className="text-xs text-slate-600 space-y-1">
                  <div className="flex items-center space-x-2">
                    <div
                      className={`w-2 h-2 rounded-full ${formData.password.length >= 8 ? 'bg-green-500' : 'bg-slate-300'}`}
                    ></div>
                    <span>At least 8 characters</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div
                      className={`w-2 h-2 rounded-full ${/[A-Z]/.test(formData.password) ? 'bg-green-500' : 'bg-slate-300'}`}
                    ></div>
                    <span>One uppercase letter</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div
                      className={`w-2 h-2 rounded-full ${/[0-9]/.test(formData.password) ? 'bg-green-500' : 'bg-slate-300'}`}
                    ></div>
                    <span>One number</span>
                  </div>
                </div>
              </div>
            )}
          </div>

          <Input
            label="Confirm Password"
            type="password"
            value={confirmPassword}
            onChange={e => {
              setConfirmPassword(e.target.value)
              if (errors.confirmPassword) {
                setErrors(prev => ({ ...prev, confirmPassword: undefined }))
              }
            }}
            placeholder="Confirm your password"
            autoComplete="new-password"
            error={errors.confirmPassword}
            icon={<PasswordIcon />}
            variant="floating"
            required
          />
        </div>

        <Button
          type="submit"
          className="w-full"
          loading={loading}
          size="lg"
          variant="gradient"
          icon={!loading ? <StarIcon /> : undefined}
          iconPosition="right"
        >
          {loading ? 'Creating Account...' : 'Create Account'}
        </Button>
      </form>

      {/* Divider */}
      <div className="relative my-8">
        <div className="absolute inset-0 flex items-center">
          <div className="w-full border-t border-slate-300"></div>
        </div>
        <div className="relative flex justify-center text-sm">
          <span className="px-4 bg-white text-slate-600 font-medium">or</span>
        </div>
      </div>

      {/* Social Signup Options */}
      <div className="space-y-3">
        <Button type="button" variant="outline" className="w-full border-slate-300 hover:border-lime-600" size="lg">
          <div className="flex items-center justify-center space-x-2">
            <svg className="w-5 h-5 mr-2" viewBox="0 0 24 24">
              <path
                fill="#4285F4"
                d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
              />
              <path
                fill="#34A853"
                d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
              />
              <path
                fill="#FBBC05"
                d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
              />
              <path
                fill="#EA4335"
                d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
              />
            </svg>
            <span>Continue with Google</span>
          </div>
        </Button>
      </div>

      {/* Switch to Login */}
      <div className="mt-8 text-center">
        <p className="text-sm text-slate-700">
          Already have an account?{' '}
          <button
            type="button"
            onClick={onSwitchToLogin}
            className="text-lime-600 hover:text-lime-700 font-semibold transition-colors"
          >
            Sign in here
          </button>
        </p>
      </div>
    </AuthCard>
  )
}
