import React, { useState, useEffect, useRef } from 'react'

interface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  label?: string
  error?: string
  helperText?: string
  icon?: React.ReactNode
  variant?: 'default' | 'floating' | 'modern'
}

export const Input: React.FC<InputProps> = ({
  label,
  error,
  helperText,
  icon,
  variant = 'modern',
  className = '',
  id,
  type = 'text',
  ...props
}) => {
  const [isFocused, setIsFocused] = useState(false)
  const [hasValue, setHasValue] = useState(false)
  const inputRef = useRef<HTMLInputElement>(null)
  const inputId = id || `input-${Math.random().toString(36).substring(2, 11)}`

  // Check for initial value and auto-filled values
  useEffect(() => {
    const checkValue = () => {
      if (inputRef.current) {
        const value = inputRef.current.value
        setHasValue(value.length > 0)
      }
    }

    // Check immediately
    checkValue()

    // Check after a short delay to catch auto-filled values
    const timeoutId = setTimeout(checkValue, 100)

    return () => clearTimeout(timeoutId)
  }, [props.value])

  // Also check for auto-filled values periodically when focused
  useEffect(() => {
    if (!isFocused) return

    const intervalId = setInterval(() => {
      if (inputRef.current) {
        const value = inputRef.current.value
        setHasValue(value.length > 0)
      }
    }, 100)

    return () => clearInterval(intervalId)
  }, [isFocused])

  const handleFocus = () => setIsFocused(true)
  const handleBlur = (e: React.FocusEvent<HTMLInputElement>) => {
    setIsFocused(false)
    setHasValue(e.target.value.length > 0)
    props.onBlur?.(e)
  }

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setHasValue(e.target.value.length > 0)
    props.onChange?.(e)
  }

  if (variant === 'floating') {
    return (
      <div className="relative space-y-1">
        <div className="relative">
          {icon && <div className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-600 z-10">{icon}</div>}
          <input
            ref={inputRef}
            id={inputId}
            type={type}
            className={`
              w-full pt-[24px] pb-[10px] ${icon ? 'pl-10' : ''}
              bg-white border-2 border-lime-600/50 rounded-xl
              text-slate-900 placeholder-transparent
              focus:outline-none focus:border-lime-600 focus:ring-2 focus:ring-lime-100
              transition-all duration-300 ease-in-out
              hover:border-lime-400 hover:shadow-md
              ${error ? 'border-red-500 focus:border-red-500 focus:ring-red-100' : ''}
              ${isFocused ? 'shadow-lg transform scale-[1.02] border-lime-600' : 'shadow-sm'}
              ${className}
            `.trim()}
            placeholder={label || ''}
            onFocus={handleFocus}
            onBlur={handleBlur}
            onChange={handleChange}
            aria-invalid={error ? 'true' : 'false'}
            aria-describedby={error ? `${inputId}-error` : helperText ? `${inputId}-helper` : undefined}
            {...props}
          />
          {label && (
            <label
              htmlFor={inputId}
              className={`
                absolute left-4 transition-all duration-300 ease-in-out pointer-events-none
                ${icon ? 'left-10' : 'left-4'}
                ${
                  isFocused || hasValue || props.value
                    ? 'top-2 text-xs text-lime-600 font-semibold'
                    : 'top-1/2 transform -translate-y-1/2 text-slate-600'
                }
              `.trim()}
            >
              {label}
            </label>
          )}
        </div>
        {error && (
          <p
            id={`${inputId}-error`}
            className="text-sm text-red-600 animate-slide-in-left flex items-center gap-1"
            role="alert"
          >
            <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
              <path
                fillRule="evenodd"
                d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z"
                clipRule="evenodd"
              />
            </svg>
            {error}
          </p>
        )}
        {helperText && !error && (
          <p id={`${inputId}-helper`} className="text-sm text-slate-600">
            {helperText}
          </p>
        )}
      </div>
    )
  }

  // Modern variant (default)
  return (
    <div className="space-y-2">
      {label && (
        <label htmlFor={inputId} className="block text-sm font-semibold text-slate-900">
          {label}
        </label>
      )}
      <div className="relative">
        {icon && <div className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-600">{icon}</div>}
        <input
          id={inputId}
          type={type}
          className={`
            w-full pt-[24px] pb-[10px] ${icon ? 'pl-10' : ''}
            bg-white border-2 border-slate-300 rounded-xl
            text-slate-900 placeholder-slate-500
            focus:outline-none focus:border-lime-600 focus:ring-2 focus:ring-lime-100
            transition-all duration-300 ease-in-out
            hover:border-slate-400 hover:shadow-md
            ${error ? 'border-red-500 focus:border-red-500 focus:ring-red-100' : ''}
            ${isFocused ? 'shadow-lg border-lime-600' : ''}
            ${className}
          `.trim()}
          onFocus={handleFocus}
          onBlur={handleBlur}
          onChange={handleChange}
          aria-invalid={error ? 'true' : 'false'}
          aria-describedby={error ? `${inputId}-error` : helperText ? `${inputId}-helper` : undefined}
          {...props}
        />
      </div>
      {error && (
        <p
          id={`${inputId}-error`}
          className="text-sm text-red-600 animate-slide-in-left flex items-center gap-1"
          role="alert"
        >
          <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
            <path
              fillRule="evenodd"
              d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z"
              clipRule="evenodd"
            />
          </svg>
          {error}
        </p>
      )}
      {helperText && !error && (
        <p id={`${inputId}-helper`} className="text-sm text-slate-600">
          {helperText}
        </p>
      )}
    </div>
  )
}
