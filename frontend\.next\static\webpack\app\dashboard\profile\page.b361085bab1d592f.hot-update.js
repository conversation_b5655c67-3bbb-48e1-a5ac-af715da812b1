"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/profile/page",{

/***/ "(app-pages-browser)/./src/components/ui/UserDropdown.tsx":
/*!********************************************!*\
  !*** ./src/components/ui/UserDropdown.tsx ***!
  \********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   UserDropdown: () => (/* binding */ UserDropdown)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _hooks_useClickOutside__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/hooks/useClickOutside */ \"(app-pages-browser)/./src/hooks/useClickOutside.ts\");\n/* __next_internal_client_entry_do_not_use__ UserDropdown auto */ \nvar _s = $RefreshSig$();\n\n\n\nconst UserDropdown = (param)=>{\n    let { user, onLogout, className = '' } = param;\n    _s();\n    const [isOpen, setIsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const dropdownRef = (0,_hooks_useClickOutside__WEBPACK_IMPORTED_MODULE_3__.useClickOutside)({\n        \"UserDropdown.useClickOutside[dropdownRef]\": ()=>setIsOpen(false)\n    }[\"UserDropdown.useClickOutside[dropdownRef]\"]);\n    const toggleDropdown = ()=>setIsOpen(!isOpen);\n    const menuItems = [\n        {\n            label: 'Back to Homepage',\n            href: '/',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: \"w-4 h-4\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\components\\\\ui\\\\UserDropdown.tsx\",\n                    lineNumber: 34,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\components\\\\ui\\\\UserDropdown.tsx\",\n                lineNumber: 33,\n                columnNumber: 9\n            }, undefined)\n        },\n        {\n            label: 'Dashboard',\n            href: '/dashboard',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: \"w-4 h-4\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\",\n                        strokeWidth: 2,\n                        d: \"M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\components\\\\ui\\\\UserDropdown.tsx\",\n                        lineNumber: 48,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\",\n                        strokeWidth: 2,\n                        d: \"M8 5a2 2 0 012-2h4a2 2 0 012 2v6H8V5z\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\components\\\\ui\\\\UserDropdown.tsx\",\n                        lineNumber: 54,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\components\\\\ui\\\\UserDropdown.tsx\",\n                lineNumber: 47,\n                columnNumber: 9\n            }, undefined)\n        },\n        {\n            label: 'Profile Settings',\n            href: '/dashboard/profile',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: \"w-4 h-4\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\components\\\\ui\\\\UserDropdown.tsx\",\n                    lineNumber: 68,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\components\\\\ui\\\\UserDropdown.tsx\",\n                lineNumber: 67,\n                columnNumber: 9\n            }, undefined)\n        },\n        {\n            label: 'Account Settings',\n            href: '/dashboard/settings',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: \"w-4 h-4\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\",\n                        strokeWidth: 2,\n                        d: \"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\components\\\\ui\\\\UserDropdown.tsx\",\n                        lineNumber: 82,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\",\n                        strokeWidth: 2,\n                        d: \"M15 12a3 3 0 11-6 0 3 3 0 016 0z\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\components\\\\ui\\\\UserDropdown.tsx\",\n                        lineNumber: 88,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\components\\\\ui\\\\UserDropdown.tsx\",\n                lineNumber: 81,\n                columnNumber: 9\n            }, undefined)\n        },\n        {\n            label: '',\n            divider: true\n        },\n        {\n            label: 'Logout',\n            onClick: ()=>{\n                onLogout();\n                setIsOpen(false);\n            },\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: \"w-4 h-4\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\components\\\\ui\\\\UserDropdown.tsx\",\n                    lineNumber: 104,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\components\\\\ui\\\\UserDropdown.tsx\",\n                lineNumber: 103,\n                columnNumber: 9\n            }, undefined)\n        }\n    ];\n    const handleMenuItemClick = (item)=>{\n        if (item.onClick) {\n            item.onClick();\n        }\n        setIsOpen(false);\n    };\n    // Get user initials for avatar\n    const getUserInitials = ()=>{\n        if (user.name) {\n            return user.name.split(' ').map((n)=>n.charAt(0)).join('').toUpperCase().slice(0, 2);\n        }\n        return user.email.charAt(0).toUpperCase();\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative \".concat(className),\n        ref: dropdownRef,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: toggleDropdown,\n                className: \"flex items-center space-x-3 p-2 rounded-lg hover:bg-slate-100 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2\",\n                \"aria-expanded\": isOpen,\n                \"aria-haspopup\": \"true\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-10 h-10 bg-gradient-to-r from-blue-900 to-slate-800 rounded-full flex items-center justify-center text-white font-semibold shadow-md\",\n                        children: getUserInitials()\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\components\\\\ui\\\\UserDropdown.tsx\",\n                        lineNumber: 145,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"hidden sm:block text-left\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm font-semibold text-slate-700 leading-tight\",\n                                children: user.name || user.email\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\components\\\\ui\\\\UserDropdown.tsx\",\n                                lineNumber: 151,\n                                columnNumber: 11\n                            }, undefined),\n                            user.name && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xs text-slate-500 leading-tight\",\n                                children: user.email\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\components\\\\ui\\\\UserDropdown.tsx\",\n                                lineNumber: 152,\n                                columnNumber: 25\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\components\\\\ui\\\\UserDropdown.tsx\",\n                        lineNumber: 150,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4 text-slate-600 transition-transform duration-200 \".concat(isOpen ? 'rotate-180' : ''),\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M19 9l-7 7-7-7\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\components\\\\ui\\\\UserDropdown.tsx\",\n                            lineNumber: 162,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\components\\\\ui\\\\UserDropdown.tsx\",\n                        lineNumber: 156,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\components\\\\ui\\\\UserDropdown.tsx\",\n                lineNumber: 138,\n                columnNumber: 7\n            }, undefined),\n            isOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute right-0 mt-2 w-64 bg-white rounded-xl shadow-xl border border-slate-200 py-2 z-50 animate-in slide-in-from-top-2 duration-200\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"px-4 py-3 border-b border-slate-100\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-12 h-12 bg-gradient-to-r from-blue-900 to-slate-800 rounded-full flex items-center justify-center text-white font-semibold\",\n                                    children: getUserInitials()\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\components\\\\ui\\\\UserDropdown.tsx\",\n                                    lineNumber: 172,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 min-w-0\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm font-semibold text-slate-900 truncate\",\n                                            children: user.name || 'User'\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\components\\\\ui\\\\UserDropdown.tsx\",\n                                            lineNumber: 176,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-slate-500 truncate\",\n                                            children: user.email\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\components\\\\ui\\\\UserDropdown.tsx\",\n                                            lineNumber: 177,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\components\\\\ui\\\\UserDropdown.tsx\",\n                                    lineNumber: 175,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\components\\\\ui\\\\UserDropdown.tsx\",\n                            lineNumber: 171,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\components\\\\ui\\\\UserDropdown.tsx\",\n                        lineNumber: 170,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"py-1\",\n                        children: menuItems.map((item, index)=>{\n                            if (item.divider) {\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"border-t border-slate-100 my-1\"\n                                }, index, false, {\n                                    fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\components\\\\ui\\\\UserDropdown.tsx\",\n                                    lineNumber: 186,\n                                    columnNumber: 24\n                                }, undefined);\n                            }\n                            const content = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3 px-4 py-3 text-sm text-slate-700 hover:bg-slate-50 hover:text-blue-900 transition-colors duration-150 cursor-pointer\",\n                                children: [\n                                    item.icon && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-slate-500 group-hover:text-blue-600\",\n                                        children: item.icon\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\components\\\\ui\\\\UserDropdown.tsx\",\n                                        lineNumber: 191,\n                                        columnNumber: 33\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium\",\n                                        children: item.label\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\components\\\\ui\\\\UserDropdown.tsx\",\n                                        lineNumber: 192,\n                                        columnNumber: 19\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\components\\\\ui\\\\UserDropdown.tsx\",\n                                lineNumber: 190,\n                                columnNumber: 17\n                            }, undefined);\n                            if (item.href) {\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: item.href,\n                                    className: \"block group\",\n                                    onClick: ()=>handleMenuItemClick(item),\n                                    children: content\n                                }, index, false, {\n                                    fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\components\\\\ui\\\\UserDropdown.tsx\",\n                                    lineNumber: 198,\n                                    columnNumber: 19\n                                }, undefined);\n                            }\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>handleMenuItemClick(item),\n                                className: \"w-full text-left group\",\n                                children: content\n                            }, index, false, {\n                                fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\components\\\\ui\\\\UserDropdown.tsx\",\n                                lineNumber: 205,\n                                columnNumber: 17\n                            }, undefined);\n                        })\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\components\\\\ui\\\\UserDropdown.tsx\",\n                        lineNumber: 183,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\components\\\\ui\\\\UserDropdown.tsx\",\n                lineNumber: 168,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\components\\\\ui\\\\UserDropdown.tsx\",\n        lineNumber: 136,\n        columnNumber: 5\n    }, undefined);\n};\n_s(UserDropdown, \"HWchHsdssuVa1g4QcDHt3AZuvvM=\", false, function() {\n    return [\n        _hooks_useClickOutside__WEBPACK_IMPORTED_MODULE_3__.useClickOutside\n    ];\n});\n_c = UserDropdown;\nvar _c;\n$RefreshReg$(_c, \"UserDropdown\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/UserDropdown.tsx\n"));

/***/ })

});