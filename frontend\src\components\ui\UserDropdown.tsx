'use client'

import React, { useState } from 'react'
import Link from 'next/link'
import { User } from '@/types/auth'
import { useClickOutside } from '@/hooks/useClickOutside'

interface UserDropdownProps {
  user: User
  onLogout: () => void
  className?: string
}

interface DropdownMenuItem {
  label: string
  href?: string
  onClick?: () => void
  icon?: React.ReactNode
  divider?: boolean
}

export const UserDropdown: React.FC<UserDropdownProps> = ({ user, onLogout, className = '' }) => {
  const [isOpen, setIsOpen] = useState(false)
  const dropdownRef = useClickOutside<HTMLDivElement>(() => setIsOpen(false))

  const toggleDropdown = () => setIsOpen(!isOpen)

  const menuItems: DropdownMenuItem[] = [
    {
      label: 'Back to Homepage',
      href: '/',
      icon: (
        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"
          />
        </svg>
      )
    },
    {
      label: 'Dashboard',
      href: '/dashboard',
      icon: (
        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"
          />
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M8 5a2 2 0 012-2h4a2 2 0 012 2v6H8V5z"
          />
        </svg>
      )
    },
    {
      label: 'Profile Settings',
      href: '/dashboard/profile',
      icon: (
        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
          />
        </svg>
      )
    },
    {
      label: 'Account Settings',
      href: '/dashboard/settings',
      icon: (
        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"
          />
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
        </svg>
      )
    },
    {
      label: '',
      divider: true
    },
    {
      label: 'Logout',
      onClick: () => {
        onLogout()
        setIsOpen(false)
      },
      icon: (
        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"
          />
        </svg>
      )
    }
  ]

  const handleMenuItemClick = (item: DropdownMenuItem) => {
    if (item.onClick) {
      item.onClick()
    }
    setIsOpen(false)
  }

  // Get user initials for avatar
  const getUserInitials = () => {
    if (user.name) {
      return user.name
        .split(' ')
        .map(n => n.charAt(0))
        .join('')
        .toUpperCase()
        .slice(0, 2)
    }
    return user.email.charAt(0).toUpperCase()
  }

  return (
    <div className={`relative ${className}`} ref={dropdownRef}>
      {/* Dropdown Trigger */}
      <button
        onClick={toggleDropdown}
        className="flex items-center space-x-3 p-2 rounded-lg hover:bg-slate-100 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
        aria-expanded={isOpen}
        aria-haspopup="true"
      >
        {/* User Avatar */}
        <div className="w-10 h-10 bg-gradient-to-r from-blue-900 to-slate-800 rounded-full flex items-center justify-center text-white font-semibold shadow-md">
          {getUserInitials()}
        </div>

        {/* User Info - Hidden on small screens */}
        <div className="hidden sm:block text-left">
          <p className="text-sm font-semibold text-slate-700 leading-tight">{user.name || user.email}</p>
          {user.name && <p className="text-xs text-slate-500 leading-tight">{user.email}</p>}
        </div>

        {/* Dropdown Arrow */}
        <svg
          className={`w-4 h-4 text-slate-600 transition-transform duration-200 ${isOpen ? 'rotate-180' : ''}`}
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
        </svg>
      </button>

      {/* Dropdown Menu */}
      {isOpen && (
        <div className="absolute right-0 mt-2 w-64 bg-white rounded-xl shadow-xl border border-slate-200 py-2 z-50 animate-in slide-in-from-top-2 duration-200">
          {/* User Info Header */}
          <div className="px-4 py-3 border-b border-slate-100">
            <div className="flex items-center space-x-3">
              <div className="w-12 h-12 bg-gradient-to-r from-blue-900 to-slate-800 rounded-full flex items-center justify-center text-white font-semibold">
                {getUserInitials()}
              </div>
              <div className="flex-1 min-w-0">
                <p className="text-sm font-semibold text-slate-900 truncate">{user.name || 'User'}</p>
                <p className="text-xs text-slate-500 truncate">{user.email}</p>
              </div>
            </div>
          </div>

          {/* Menu Items */}
          <div className="py-1">
            {menuItems.map((item, index) => {
              if (item.divider) {
                return <div key={index} className="border-t border-slate-100 my-1" />
              }

              const content = (
                <div className="flex items-center space-x-3 px-4 py-3 text-sm text-slate-700 hover:bg-slate-50 hover:text-blue-900 transition-colors duration-150 cursor-pointer">
                  {item.icon && <span className="text-slate-500 group-hover:text-blue-600">{item.icon}</span>}
                  <span className="font-medium">{item.label}</span>
                </div>
              )

              if (item.href) {
                return (
                  <Link key={index} href={item.href} className="block group" onClick={() => handleMenuItemClick(item)}>
                    {content}
                  </Link>
                )
              }

              return (
                <button key={index} onClick={() => handleMenuItemClick(item)} className="w-full text-left group">
                  {content}
                </button>
              )
            })}
          </div>
        </div>
      )}
    </div>
  )
}
