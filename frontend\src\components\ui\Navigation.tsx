'use client'

import React from 'react'
import Image from 'next/image'
import Link from 'next/link'
import { useAuth } from '@/contexts/AuthContext'
import { useRouter } from 'next/navigation'
import { Button } from '@/components/ui/Button'
import { UserDropdown } from '@/components/ui/UserDropdown'

interface NavigationProps {
  className?: string
}

export const Navigation: React.FC<NavigationProps> = ({ className = '' }) => {
  const { user, isAuthenticated, logout } = useAuth()
  const router = useRouter()
  const [isMobileMenuOpen, setIsMobileMenuOpen] = React.useState(false)

  const handleLogout = () => {
    logout()
    router.push('/')
  }

  const navigationLinks = [
    { href: '/', label: 'Home' },
    { href: '#about-us', label: 'About Us' },
    { href: '#properties', label: 'Properties' },
    { href: '#explore-places', label: 'Explore Places' },
    { href: '#developers', label: 'Developers' }
  ]

  return (
    <nav className={`bg-white/95 backdrop-blur-md shadow-lg border-b border-slate-200 sticky top-0 z-50 ${className}`}>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-20">
          {/* Logo */}
          <div className="flex items-center">
            <Link href="/" className="flex items-center group">
              <Image
                src="/images/logo.png"
                alt="MyRealHub Logo"
                width={259}
                height={64}
                className="h-16 w-auto transition-transform duration-300 group-hover:scale-105"
                priority
              />
            </Link>
          </div>

          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center space-x-8">
            {navigationLinks.map(link => (
              <Link
                key={link.href}
                href={link.href}
                className="text-slate-700 hover:text-blue-900 transition-all duration-300 font-semibold relative group"
              >
                {link.label}
                <span className="absolute -bottom-1 left-0 w-0 h-0.5 bg-blue-900 transition-all duration-300 group-hover:w-full"></span>
              </Link>
            ))}
          </div>

          {/* User Actions */}
          <div className="flex items-center space-x-4">
            {isAuthenticated && user ? (
              // Authenticated user display - User Dropdown
              <UserDropdown user={user} onLogout={handleLogout} />
            ) : (
              // Unauthenticated user display
              <>
                <Link href="/auth" className="text-slate-700 hover:text-blue-900 transition-colors font-semibold">
                  Sign In
                </Link>
                <Button
                  variant="gradient"
                  size="md"
                  className="bg-gradient-to-r from-blue-900 to-slate-800 hover:from-slate-800 hover:to-blue-900 shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300"
                >
                  <Link href="/auth" className="text-white">
                    Get Started
                  </Link>
                </Button>
              </>
            )}
          </div>

          {/* Mobile menu button */}
          <div className="md:hidden">
            <button
              type="button"
              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
              className="text-slate-700 hover:text-blue-900 focus:outline-none focus:text-blue-900 p-2 rounded-lg hover:bg-slate-100 transition-colors"
              aria-label="Toggle menu"
              aria-expanded={isMobileMenuOpen}
            >
              {isMobileMenuOpen ? (
                <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              ) : (
                <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                </svg>
              )}
            </button>
          </div>
        </div>

        {/* Mobile Menu */}
        {isMobileMenuOpen && (
          <div className="md:hidden bg-white/95 backdrop-blur-md border-t border-slate-200 shadow-lg">
            <div className="px-4 py-4 space-y-2">
              {navigationLinks.map(link => (
                <Link
                  key={link.href}
                  href={link.href}
                  className="block px-4 py-3 text-slate-700 hover:text-blue-900 hover:bg-blue-50 rounded-lg transition-all duration-300 font-semibold"
                  onClick={() => setIsMobileMenuOpen(false)}
                >
                  {link.label}
                </Link>
              ))}

              <div className="border-t border-slate-200 pt-4 mt-4 space-y-2">
                {isAuthenticated && user ? (
                  // Authenticated mobile menu
                  <>
                    <div className="px-4 py-3 text-slate-700 font-semibold">Welcome, {user.name || user.email}</div>
                    <button
                      onClick={() => {
                        handleLogout()
                        setIsMobileMenuOpen(false)
                      }}
                      className="block w-full text-left px-4 py-3 text-slate-700 hover:text-blue-900 hover:bg-blue-50 rounded-lg transition-all duration-300 font-semibold"
                    >
                      Logout
                    </button>
                  </>
                ) : (
                  // Unauthenticated mobile menu
                  <>
                    <Link
                      href="/auth"
                      className="block px-4 py-3 text-slate-700 hover:text-blue-900 hover:bg-blue-50 rounded-lg transition-all duration-300 font-semibold"
                      onClick={() => setIsMobileMenuOpen(false)}
                    >
                      Sign In
                    </Link>
                    <Link
                      href="/auth"
                      className="block px-4 py-3 bg-gradient-to-r from-blue-900 to-slate-800 text-white hover:from-slate-800 hover:to-blue-900 rounded-lg transition-all duration-300 text-center font-semibold shadow-lg"
                      onClick={() => setIsMobileMenuOpen(false)}
                    >
                      Get Started
                    </Link>
                  </>
                )}
              </div>
            </div>
          </div>
        )}
      </div>
    </nav>
  )
}
