'use client'

import React, { useState } from 'react'
import { useRouter } from 'next/navigation'
import { LoginForm } from '@/components/auth/LoginForm'
import { SignupForm } from '@/components/auth/SignupForm'
import { AuthPageWrapper } from '@/components/auth/ProtectedRoute'
import {
  FloatingElements,
  ArchitecturalPattern,
  PropertyIcon,
  KeyIcon,
  BuildingIcon
} from '@/components/ui/RealEstateIcons'

type AuthMode = 'login' | 'signup'

export default function AuthPage() {
  const [mode, setMode] = useState<AuthMode>('login')
  const router = useRouter()

  const handleAuthSuccess = () => {
    // Redirect to dashboard or home page after successful authentication
    router.push('/')
  }

  return (
    <AuthPageWrapper>
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-slate-100 flex">
        {/* Left Side - Branding and Visual */}
        <div className="hidden lg:flex lg:w-1/2 relative overflow-hidden">
          {/* Enhanced Background with Multiple Layers */}
          <div className="absolute inset-0 bg-gradient-to-br from-blue-600 via-blue-700 to-slate-800"></div>
          <div className="absolute inset-0 bg-gradient-to-tr from-blue-500/20 via-transparent to-slate-600/10"></div>
          <div className="absolute inset-0 bg-gradient-to-bl from-transparent via-blue-400/10 to-slate-700/80"></div>
          <div className="absolute inset-0 bg-gradient-to-tl from-blue-300/15 via-transparent to-blue-200/8"></div>

          {/* Real Estate Themed Floating Elements */}
          <FloatingElements />

          {/* Content */}
          <div className="relative z-10 flex flex-col justify-center items-start p-16 text-white">
            <div className="animate-slide-in-left">
              <h1 className="text-5xl font-bold mb-6 leading-tight">
                Welcome to <br />
                <span className="bg-gradient-to-r from-blue-200 via-white to-blue-100 bg-clip-text text-transparent">
                  MyRealHub
                </span>
              </h1>
              <p className="text-xl mb-8 text-white/90 leading-relaxed">
                Your premium real estate management platform. Discover, manage, and grow your property portfolio with
                confidence.
              </p>

              {/* Feature highlights */}
              <div className="space-y-5">
                <div className="flex items-center space-x-4 group">
                  <div className="w-4 h-4 bg-blue-300 rounded-full shadow-lg animate-pulse-slow group-hover:scale-125 transition-transform"></div>
                  <span className="text-white/90 font-medium group-hover:text-blue-200 transition-colors">
                    Advanced Property Analytics
                  </span>
                </div>
                <div className="flex items-center space-x-4 group">
                  <div
                    className="w-4 h-4 bg-slate-300 rounded-full shadow-lg animate-pulse-slow group-hover:scale-125 transition-transform"
                    style={{ animationDelay: '0.5s' }}
                  ></div>
                  <span className="text-white/90 font-medium group-hover:text-slate-200 transition-colors">
                    Multi-tenant Management
                  </span>
                </div>
                <div className="flex items-center space-x-4 group">
                  <div
                    className="w-4 h-4 bg-blue-200 rounded-full shadow-lg animate-pulse-slow group-hover:scale-125 transition-transform"
                    style={{ animationDelay: '1s' }}
                  ></div>
                  <span className="text-white/90 font-medium group-hover:text-blue-100 transition-colors">
                    Real-time Market Insights
                  </span>
                </div>
              </div>
            </div>
          </div>

          {/* Architectural Pattern Overlay */}
          <div className="absolute bottom-0 right-0 w-80 h-80 text-blue-200/70">
            <ArchitecturalPattern />
          </div>
        </div>

        {/* Right Side - Authentication Forms */}
        <div className="w-full lg:w-1/2 flex items-center justify-center p-8 lg:p-16 relative bg-white/95">
          {/* Enhanced background pattern for right side */}
          <div className="absolute inset-0 opacity-10">
            <div className="absolute top-10 right-10 w-32 h-32 text-blue-600">
              <PropertyIcon className="w-full h-full" />
            </div>
            <div className="absolute bottom-20 left-10 w-24 h-24 text-slate-600">
              <KeyIcon className="w-full h-full" />
            </div>
            <div className="absolute top-1/2 left-5 w-20 h-20 text-blue-500">
              <BuildingIcon className="w-full h-full" />
            </div>
          </div>

          <div className="w-full max-w-md animate-slide-in-right relative z-10">
            {/* Mobile Logo */}
            <div className="lg:hidden text-center mb-8">
              <h1 className="text-4xl font-bold text-slate-900 mb-2">
                <span className="bg-gradient-to-r from-blue-600 via-blue-700 to-slate-800 bg-clip-text text-transparent">
                  MyRealHub
                </span>
              </h1>
              <p className="text-slate-700 font-medium">Premium Real Estate Management</p>
            </div>

            {/* Authentication Forms */}
            <div className="space-y-6">
              {mode === 'login' ? (
                <LoginForm onSuccess={handleAuthSuccess} onSwitchToSignup={() => setMode('signup')} />
              ) : (
                <SignupForm onSuccess={handleAuthSuccess} onSwitchToLogin={() => setMode('login')} />
              )}
            </div>

            {/* Footer */}
            <div className="mt-8 text-center">
              <p className="text-xs text-white/60">
                By continuing, you agree to our{' '}
                <a href="#" className="text-real-estate-gold hover:text-real-estate-gold-light transition-colors">
                  Terms of Service
                </a>{' '}
                and{' '}
                <a href="#" className="text-real-estate-gold hover:text-real-estate-gold-light transition-colors">
                  Privacy Policy
                </a>
              </p>
            </div>
          </div>
        </div>
      </div>
    </AuthPageWrapper>
  )
}
