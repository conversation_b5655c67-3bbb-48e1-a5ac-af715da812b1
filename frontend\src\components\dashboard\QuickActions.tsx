'use client'

import React from 'react'
import Link from 'next/link'
import { DashboardCard } from '@/components/ui/Card'

interface QuickAction {
  id: string
  title: string
  description: string
  href: string
  icon: React.ReactNode
  color: 'blue' | 'green' | 'purple' | 'orange' | 'red'
}

interface QuickActionsProps {
  className?: string
}

export const QuickActions: React.FC<QuickActionsProps> = ({ className = '' }) => {
  const quickActions: QuickAction[] = [
    {
      id: 'add-property',
      title: 'Add Property',
      description: 'List a new property',
      href: '/dashboard/properties/new',
      color: 'blue',
      icon: (
        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
        </svg>
      )
    },
    {
      id: 'view-analytics',
      title: 'View Analytics',
      description: 'Check performance metrics',
      href: '/dashboard/analytics',
      color: 'green',
      icon: (
        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"
          />
        </svg>
      )
    },
    {
      id: 'messages',
      title: 'Messages',
      description: 'Check new messages',
      href: '/dashboard/messages',
      color: 'purple',
      icon: (
        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"
          />
        </svg>
      )
    },
    {
      id: 'profile',
      title: 'Update Profile',
      description: 'Manage your account',
      href: '/dashboard/profile',
      color: 'orange',
      icon: (
        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
          />
        </svg>
      )
    }
  ]

  const getColorClasses = (color: QuickAction['color']) => {
    const colorMap = {
      blue: {
        bg: 'bg-blue-50 hover:bg-blue-100',
        icon: 'bg-blue-500 text-white',
        border: 'border-blue-200 hover:border-blue-300'
      },
      green: {
        bg: 'bg-green-50 hover:bg-green-100',
        icon: 'bg-green-500 text-white',
        border: 'border-green-200 hover:border-green-300'
      },
      purple: {
        bg: 'bg-purple-50 hover:bg-purple-100',
        icon: 'bg-purple-500 text-white',
        border: 'border-purple-200 hover:border-purple-300'
      },
      orange: {
        bg: 'bg-orange-50 hover:bg-orange-100',
        icon: 'bg-orange-500 text-white',
        border: 'border-orange-200 hover:border-orange-300'
      },
      red: {
        bg: 'bg-red-50 hover:bg-red-100',
        icon: 'bg-red-500 text-white',
        border: 'border-red-200 hover:border-red-300'
      }
    }
    return colorMap[color]
  }

  return (
    <DashboardCard
      title="Quick Actions"
      subtitle="Common tasks and shortcuts"
      className={className}
      icon={
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
        </svg>
      }
    >
      <div className="grid grid-cols-1 sm:grid-cols-2 xl:grid-cols-1 2xl:grid-cols-2 gap-4">
        {quickActions.map(action => {
          const colors = getColorClasses(action.color)

          return (
            <Link
              key={action.id}
              href={action.href}
              className={`
                block p-4 rounded-lg border-2 transition-all duration-200
                ${colors.bg} ${colors.border}
                hover:shadow-md hover:scale-105
                focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2
              `}
            >
              <div className="flex items-start space-x-3">
                <div className={`p-2 rounded-lg ${colors.icon}`}>{action.icon}</div>
                <div className="flex-1 min-w-0">
                  <h4 className="text-sm font-semibold text-slate-900 mb-1">{action.title}</h4>
                  <p className="text-xs text-slate-600">{action.description}</p>
                </div>
                <svg className="w-4 h-4 text-slate-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                </svg>
              </div>
            </Link>
          )
        })}
      </div>
    </DashboardCard>
  )
}
