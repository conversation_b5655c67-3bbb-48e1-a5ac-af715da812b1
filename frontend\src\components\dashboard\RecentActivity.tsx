'use client'

import React from 'react'
import Link from 'next/link'
import { DashboardCard } from '@/components/ui/Card'

interface ActivityItem {
  id: string
  type: 'property' | 'message' | 'analytics' | 'profile' | 'system'
  title: string
  description: string
  timestamp: string
  href?: string
  icon: React.ReactNode
}

interface RecentActivityProps {
  className?: string
}

export const RecentActivity: React.FC<RecentActivityProps> = ({ className = '' }) => {
  // Mock data - in a real app, this would come from an API
  const activities: ActivityItem[] = [
    {
      id: '1',
      type: 'property',
      title: 'New Property Listed',
      description: 'Modern 3BR apartment in Downtown',
      timestamp: '2 hours ago',
      href: '/dashboard/properties/123',
      icon: (
        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
        </svg>
      )
    },
    {
      id: '2',
      type: 'message',
      title: 'New Message Received',
      description: 'Inquiry about Sunset Villa property',
      timestamp: '4 hours ago',
      href: '/dashboard/messages/456',
      icon: (
        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
        </svg>
      )
    },
    {
      id: '3',
      type: 'analytics',
      title: 'Weekly Report Generated',
      description: 'Property views increased by 15%',
      timestamp: '1 day ago',
      href: '/dashboard/analytics',
      icon: (
        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
        </svg>
      )
    },
    {
      id: '4',
      type: 'profile',
      title: 'Profile Updated',
      description: 'Contact information updated successfully',
      timestamp: '2 days ago',
      href: '/dashboard/profile',
      icon: (
        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
        </svg>
      )
    },
    {
      id: '5',
      type: 'system',
      title: 'System Maintenance',
      description: 'Scheduled maintenance completed',
      timestamp: '3 days ago',
      icon: (
        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
        </svg>
      )
    }
  ]

  const getActivityTypeColor = (type: ActivityItem['type']) => {
    const colorMap = {
      property: 'bg-blue-100 text-blue-600',
      message: 'bg-green-100 text-green-600',
      analytics: 'bg-purple-100 text-purple-600',
      profile: 'bg-orange-100 text-orange-600',
      system: 'bg-slate-100 text-slate-600'
    }
    return colorMap[type]
  }

  return (
    <DashboardCard 
      title="Recent Activity" 
      subtitle="Your latest actions and updates"
      className={className}
      icon={
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
      }
      action={
        <Link 
          href="/dashboard/activity" 
          className="text-sm text-blue-600 hover:text-blue-800 font-medium"
        >
          View All
        </Link>
      }
    >
      <div className="space-y-4">
        {activities.map((activity) => {
          const content = (
            <div className="flex items-start space-x-3 p-3 rounded-lg hover:bg-slate-50 transition-colors duration-150">
              <div className={`p-2 rounded-lg ${getActivityTypeColor(activity.type)}`}>
                {activity.icon}
              </div>
              <div className="flex-1 min-w-0">
                <h4 className="text-sm font-medium text-slate-900 mb-1">
                  {activity.title}
                </h4>
                <p className="text-sm text-slate-600 mb-1">
                  {activity.description}
                </p>
                <p className="text-xs text-slate-500">
                  {activity.timestamp}
                </p>
              </div>
              {activity.href && (
                <svg className="w-4 h-4 text-slate-400 mt-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                </svg>
              )}
            </div>
          )

          if (activity.href) {
            return (
              <Link
                key={activity.id}
                href={activity.href}
                className="block focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 rounded-lg"
              >
                {content}
              </Link>
            )
          }

          return (
            <div key={activity.id}>
              {content}
            </div>
          )
        })}
      </div>

      {activities.length === 0 && (
        <div className="text-center py-8">
          <svg className="w-12 h-12 text-slate-300 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <p className="text-slate-500 text-sm">No recent activity</p>
        </div>
      )}
    </DashboardCard>
  )
}
