'use client'

import React from 'react'
import { RequireAuthWrapper } from '@/components/auth/ProtectedRoute'
import { DashboardLayout } from '@/components/dashboard/DashboardLayout'
import { DashboardCard } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'

export default function MessagesPage() {
  const messages = [
    {
      id: 1,
      sender: '<PERSON>',
      subject: 'Inquiry about Sunset Villa',
      preview: 'Hi, I\'m interested in the 3-bedroom villa you have listed...',
      timestamp: '2 hours ago',
      unread: true
    },
    {
      id: 2,
      sender: '<PERSON>',
      subject: 'Property viewing request',
      preview: 'Could we schedule a viewing for the downtown apartment?',
      timestamp: '5 hours ago',
      unread: true
    },
    {
      id: 3,
      sender: '<PERSON>',
      subject: 'Price negotiation',
      preview: 'Thank you for the quick response. I would like to discuss...',
      timestamp: '1 day ago',
      unread: false
    },
    {
      id: 4,
      sender: '<PERSON>',
      subject: 'Property details',
      preview: 'Can you provide more information about the parking situation?',
      timestamp: '2 days ago',
      unread: false
    }
  ]

  const actions = (
    <Button variant="primary" size="sm">
      <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
      </svg>
      Compose
    </Button>
  )

  return (
    <RequireAuthWrapper>
      <DashboardLayout
        title="Messages"
        subtitle="Manage your property inquiries and communications"
        actions={actions}
      >
        <DashboardCard
          title="Inbox"
          subtitle={`${messages.filter(m => m.unread).length} unread messages`}
          icon={
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
            </svg>
          }
        >
          <div className="space-y-1">
            {messages.map((message) => (
              <div
                key={message.id}
                className={`p-4 rounded-lg border transition-colors duration-150 cursor-pointer hover:bg-slate-50 ${
                  message.unread 
                    ? 'bg-blue-50 border-blue-200' 
                    : 'bg-white border-slate-200'
                }`}
              >
                <div className="flex items-start justify-between">
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center space-x-2 mb-1">
                      <h4 className={`text-sm font-medium ${message.unread ? 'text-slate-900' : 'text-slate-700'}`}>
                        {message.sender}
                      </h4>
                      {message.unread && (
                        <span className="w-2 h-2 bg-blue-500 rounded-full"></span>
                      )}
                    </div>
                    <h5 className={`text-sm mb-1 ${message.unread ? 'font-medium text-slate-900' : 'text-slate-700'}`}>
                      {message.subject}
                    </h5>
                    <p className="text-sm text-slate-600 truncate">
                      {message.preview}
                    </p>
                  </div>
                  <div className="text-right ml-4">
                    <p className="text-xs text-slate-500">
                      {message.timestamp}
                    </p>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {messages.length === 0 && (
            <div className="text-center py-12">
              <svg className="w-16 h-16 text-slate-300 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
              </svg>
              <h3 className="text-lg font-medium text-slate-900 mb-2">No messages</h3>
              <p className="text-slate-600">Your messages will appear here when you receive them.</p>
            </div>
          )}
        </DashboardCard>
      </DashboardLayout>
    </RequireAuthWrapper>
  )
}
