import React from 'react'

interface CardProps {
  children: React.ReactNode
  className?: string
  variant?: 'default' | 'elevated' | 'glass' | 'premium'
  hover?: boolean
}

export const Card: React.FC<CardProps> = ({ children, className = '', variant = 'elevated', hover = false }) => {
  const baseClasses = `
    bg-white border border-slate-200 rounded-2xl
    transition-all duration-300 ease-in-out
    ${hover ? 'hover:shadow-xl hover:scale-[1.02] cursor-pointer' : ''}
  `.trim()

  const variantClasses = {
    default: 'shadow-md',
    elevated: 'shadow-xl shadow-slate-900/10',
    glass: 'glass-morphism backdrop-blur-lg',
    premium: `
      shadow-2xl shadow-slate-900/15
      bg-white border-slate-200
      ring-1 ring-slate-100
    `.trim()
  }

  return <div className={`${baseClasses} ${variantClasses[variant]} ${className}`}>{children}</div>
}

interface CardHeaderProps {
  children: React.ReactNode
  className?: string
  gradient?: boolean
}

export const CardHeader: React.FC<CardHeaderProps> = ({ children, className = '', gradient = false }) => {
  const baseClasses = `
    px-8 py-6 border-b border-slate-200
    ${gradient ? 'bg-gradient-to-r from-blue-50 to-slate-50' : ''}
  `.trim()

  return <div className={`${baseClasses} ${className}`}>{children}</div>
}

interface CardContentProps {
  children: React.ReactNode
  className?: string
  padding?: 'sm' | 'md' | 'lg' | 'xl'
}

export const CardContent: React.FC<CardContentProps> = ({ children, className = '', padding = 'lg' }) => {
  const paddingClasses = {
    sm: 'px-4 py-3',
    md: 'px-6 py-4',
    lg: 'px-8 py-6',
    xl: 'px-10 py-8'
  }

  return <div className={`${paddingClasses[padding]} ${className}`}>{children}</div>
}

interface CardFooterProps {
  children: React.ReactNode
  className?: string
  gradient?: boolean
}

export const CardFooter: React.FC<CardFooterProps> = ({ children, className = '', gradient = false }) => {
  const baseClasses = `
    px-8 py-6 border-t border-slate-200
    ${gradient ? 'bg-gradient-to-r from-slate-50 to-blue-50' : ''}
  `.trim()

  return <div className={`${baseClasses} ${className}`}>{children}</div>
}

// New enhanced card components for auth
interface AuthCardProps {
  children: React.ReactNode
  className?: string
  title?: string
  subtitle?: string
}

export const AuthCard: React.FC<AuthCardProps> = ({ children, className = '', title, subtitle }) => {
  return (
    <Card variant="premium" className={`animate-scale-in border-slate-300 ${className}`}>
      {(title || subtitle) && (
        <CardHeader gradient className="rounded-tl-2xl rounded-tr-2xl">
          {title && <h2 className="text-2xl font-bold text-slate-900 text-center">{title}</h2>}
          {subtitle && <p className="text-slate-700 text-center mt-2">{subtitle}</p>}
        </CardHeader>
      )}
      <CardContent padding="xl">{children}</CardContent>
    </Card>
  )
}

// Dashboard-specific card components
interface DashboardCardProps {
  children: React.ReactNode
  className?: string
  title?: string
  subtitle?: string
  icon?: React.ReactNode
  action?: React.ReactNode
  variant?: 'default' | 'stats' | 'activity' | 'profile'
}

export const DashboardCard: React.FC<DashboardCardProps> = ({
  children,
  className = '',
  title,
  subtitle,
  icon,
  action,
  variant = 'default'
}) => {
  const variantStyles = {
    default: 'bg-white border-slate-200',
    stats: 'bg-gradient-to-br from-blue-50 to-slate-50 border-blue-200',
    activity: 'bg-white border-slate-200',
    profile: 'bg-gradient-to-br from-slate-50 to-blue-50 border-slate-200'
  }

  return (
    <Card className={`${variantStyles[variant]} ${className}`} variant="elevated">
      {(title || subtitle || icon || action) && (
        <CardHeader className="flex flex-row items-center justify-between">
          <div className="flex items-center space-x-3">
            {icon && <div className="p-2 bg-blue-100 rounded-lg text-blue-600">{icon}</div>}
            <div>
              {title && <h3 className="text-lg font-semibold text-slate-900">{title}</h3>}
              {subtitle && <p className="text-sm text-slate-600 mt-1">{subtitle}</p>}
            </div>
          </div>
          {action && <div>{action}</div>}
        </CardHeader>
      )}
      <CardContent>{children}</CardContent>
    </Card>
  )
}

// Stats card component
interface StatsCardProps {
  title: string
  value: string | number
  change?: string
  changeType?: 'positive' | 'negative' | 'neutral'
  icon?: React.ReactNode
  className?: string
}

export const StatsCard: React.FC<StatsCardProps> = ({
  title,
  value,
  change,
  changeType = 'neutral',
  icon,
  className = ''
}) => {
  const changeColors = {
    positive: 'text-green-600 bg-green-50',
    negative: 'text-red-600 bg-red-50',
    neutral: 'text-slate-600 bg-slate-50'
  }

  return (
    <DashboardCard variant="stats" className={className}>
      <div className="flex items-center justify-between">
        <div className="flex-1">
          <p className="text-sm font-medium text-slate-600 mb-1">{title}</p>
          <p className="text-2xl font-bold text-slate-900">{value}</p>
          {change && (
            <div
              className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium mt-2 ${changeColors[changeType]}`}
            >
              {changeType === 'positive' && '↗'}
              {changeType === 'negative' && '↘'}
              {change}
            </div>
          )}
        </div>
        {icon && <div className="p-3 bg-blue-600 rounded-lg text-white">{icon}</div>}
      </div>
    </DashboardCard>
  )
}
