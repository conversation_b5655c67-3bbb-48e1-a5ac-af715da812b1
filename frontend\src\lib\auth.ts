import { AuthResponse, SignupData, LoginData, AuthError } from '@/types/auth'

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001'

class AuthService {
  async signup(data: SignupData): Promise<AuthResponse> {
    const response = await fetch(`${API_BASE_URL}/auth/signup`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(data)
    })

    if (!response.ok) {
      const error: AuthError = await response.json()
      throw new Error(error.message || 'Signup failed')
    }

    return response.json()
  }

  async login(data: LoginData): Promise<AuthResponse> {
    const response = await fetch(`${API_BASE_URL}/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(data)
    })

    if (!response.ok) {
      const error: AuthError = await response.json()
      throw new Error(error.message || 'Login failed')
    }

    return response.json()
  }

  setToken(token: string) {
    localStorage.setItem('auth_token', token)
  }

  getToken(): string | null {
    if (typeof window === 'undefined') return null
    return localStorage.getItem('auth_token')
  }

  removeToken() {
    localStorage.removeItem('auth_token')
  }

  async validateToken(): Promise<AuthResponse['user'] | null> {
    const token = this.getToken()
    if (!token) return null

    try {
      const response = await fetch(`${API_BASE_URL}/auth/me`, {
        method: 'GET',
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      })

      if (!response.ok) {
        // Token is invalid, remove it
        this.removeToken()
        return null
      }

      const userData = await response.json()
      return userData
    } catch (error) {
      console.error('Failed to validate token:', error)
      // Network error or invalid response, remove token
      this.removeToken()
      return null
    }
  }

  isAuthenticated(): boolean {
    return !!this.getToken()
  }
}

export const authService = new AuthService()
